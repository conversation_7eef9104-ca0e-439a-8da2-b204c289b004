# app_pyside6.py
import sys
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QGridLayout, QVBoxLayout, QHBoxLayout,
    QPushButton, QComboBox, QLabel, QLineEdit, QRadioButton, QGroupBox,
    QMessageBox, QApplication, QSizePolicy, QTabWidget, QFrame, QScrollArea,
    QProgressBar, QSpinBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QColor
from plotter import Plotter
from serial_manager import SerialManager
from modern_styles import ModernStyles
from modern_widgets import (
    StatusIndicator, ModernComboBox, ModernButton,
    SidebarSection, ConnectionPanel
)
# 暫時隱藏未完成的分析功能頁籤導入
# from data_analysis_tab import DataAnalysisTab
# from comparison_tab import ComparisonTab
# from trend_analysis_tab import TrendAnalysisTab
import os, csv, time
import numpy as np
from motion_metrics import MotionMetrics
from data_forwarder import DataForwarder
# 移除scipy，因為重力補償將在metrics內部完成
# from scipy.spatial.transform import Rotation as R

SENSOR_MAX = 4

class SensorCard(QWidget):
    freq_set_requested = Signal(str, str)  # mac, freq
    def __init__(self, idx, parent=None):
        super().__init__(parent)
        self.idx = idx
        self.setObjectName("sensor_card")
        self.setMinimumSize(420, 290)  # 稍微增加高度以容納進度條
        self._setup_ui()

    def _setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(6)  # 減少間距
        layout.setContentsMargins(10, 8, 10, 8)  # 減少邊距

        # 卡片標題和狀態
        header_layout = QHBoxLayout()
        title_label = QLabel(f"感測器 {self.idx + 1}")
        title_label.setObjectName("card_title")
        header_layout.addWidget(title_label)

        # 狀態指示器
        self.status_indicator = StatusIndicator()
        self.status_label = QLabel("未啟用")
        self.status_label.setObjectName("status_disconnected")
        header_layout.addWidget(self.status_indicator)
        header_layout.addWidget(self.status_label)

        # MAC選擇 - 移到同一行
        mac_label = QLabel("MAC:")
        header_layout.addWidget(mac_label)
        self.mac_combo = ModernComboBox()
        self.mac_combo.setMinimumWidth(150)
        header_layout.addWidget(self.mac_combo)
        header_layout.addStretch()
        layout.addLayout(header_layout)
        # 檔案設置
        file_layout = QHBoxLayout()
        file_layout.setSpacing(4)  # 減少間距
        file_num_label = QLabel("檔案編號:")
        file_layout.addWidget(file_num_label)
        self.file_num = QLineEdit("1")
        self.file_num.setFixedWidth(50)
        file_layout.addWidget(self.file_num)

        file_name_label = QLabel("檔案名稱:")
        file_layout.addWidget(file_name_label)
        self.file_name = QLineEdit("test")
        self.file_name.setFixedWidth(100)
        file_layout.addWidget(self.file_name)

        # 將設定頻率的輸入框和按鈕移到這裡
        self.freq_input = QLineEdit()
        self.freq_input.setPlaceholderText('Hz')
        self.freq_input.setFixedWidth(50)
        self.freq_btn = QPushButton('設定頻率')
        self.freq_btn.setFixedWidth(70)
        self.freq_btn.clicked.connect(self._on_set_freq)
        file_layout.addWidget(self.freq_input)
        file_layout.addWidget(self.freq_btn)

        file_layout.addStretch()
        layout.addLayout(file_layout)

        # 系統信息
        info_layout = QHBoxLayout()
        self.batt_label = QLabel("電量: --%")
        self.batt_label.setObjectName("metric_label")
        info_layout.addWidget(self.batt_label)

        self.fps_label = QLabel("FPS: --")
        self.fps_label.setObjectName("metric_label")
        info_layout.addWidget(self.fps_label)

        self.quat_label = QLabel("四元數: --, --, --, --")
        self.quat_label.setObjectName("metric_label")
        info_layout.addWidget(self.quat_label)
        info_layout.addStretch()
        layout.addLayout(info_layout)

        # 運動指標 - 改為單行緊湊顯示
        metrics_layout = QHBoxLayout()
        metrics_layout.setSpacing(8)
        metrics_layout.setContentsMargins(0, 0, 0, 0)

        # 創建緊湊的運動指標顯示，每個指標包含標籤和進度條
        def create_metric_widget(label_text, color, bg_color):
            widget = QWidget()
            widget_layout = QVBoxLayout(widget)
            widget_layout.setContentsMargins(0, 0, 0, 0)
            widget_layout.setSpacing(1)

            # 標籤
            label = QLabel(label_text)
            label.setObjectName("metric_value")
            label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 11px; padding: 4px 8px; background: {bg_color}; border-radius: 4px;")
            widget_layout.addWidget(label)

            # 進度條
            progress = QProgressBar()
            progress.setRange(0, 100)
            progress.setValue(0)
            progress.setTextVisible(False)
            progress.setFixedHeight(3)
            progress.setStyleSheet(f"""
                QProgressBar {{
                    border: none;
                    background: rgba(200, 200, 200, 0.3);
                    border-radius: 1px;
                }}
                QProgressBar::chunk {{
                    background: {color};
                    border-radius: 1px;
                }}
            """)
            widget_layout.addWidget(progress)

            return widget, label, progress

        # 創建各個運動指標
        speed_widget, self.max_speed_label, self.speed_progress = create_metric_widget(
            "最大速度: -- m/s", "#00aa00", "rgba(0,170,0,0.1)")
        # 暫時隱藏角度指標
        # angle_widget, self.max_angle_label, self.angle_progress = create_metric_widget(
        #     "最大角度: --°", "#aa5500", "rgba(170,85,0,0.1)")
        acc_widget, self.max_instant_acc_label, self.acc_progress = create_metric_widget(
            "最大加速度: -- m/s²", "#0055aa", "rgba(0,85,170,0.1)")
        force_widget, self.max_force_label, self.force_progress = create_metric_widget(
            "最大力度: -- g", "#aa0055", "rgba(170,0,85,0.1)")

        metrics_layout.addWidget(speed_widget)
        # metrics_layout.addWidget(angle_widget)  # 暫時隱藏
        metrics_layout.addWidget(acc_widget)
        metrics_layout.addWidget(force_widget)
        metrics_layout.addStretch()

        layout.addLayout(metrics_layout)

        # 為了兼容性，保留原有的標籤引用
        # self.max_speed_label = self.speed_card.value_label
        # self.max_angle_label = self.angle_card.value_label
        # self.max_instant_acc_label = self.acc_card.value_label
        # self.max_force_label = self.force_card.value_label
        # 圖表顯示 - 給圖表更多空間
        self.plotter = Plotter(figsize=(4.0, 2.8), dpi=100)
        self.canvas = self.plotter.get_canvas()
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.canvas.setMinimumHeight(180)  # 稍微減少最小高度，為選擇框留出空間
        layout.addWidget(self.canvas, 2)  # 增加拉伸因子，讓圖表佔用更多空間

        # 初始化屬性
        self.connected_mac = None
        self.file_path = None
        self.record_sample_counter = 0
        self.last_timestamp = None
        self.mac_combo.currentTextChanged.connect(self._on_mac_change)
        self.is_active = False
        self.cur_mode = '6d' # 保留但不再切換
        self.update_plot_unit()
        self._block_mode_signal = False
        self.last_data_mode = '6d'
        self.sample_counter = 0
        self.mac_history = []
        self._last_mac_list = []
        self.timestamp_counter = 0
        # 新增 BLE 頻率設定 UI
        self.record_buffer = []  # 暫存N筆資料
        self.record_N = 5        # 每N筆資料才寫入一次
        self.last_record_time = None
        self.last_record_ts = 0
        self._fps_counter = 0
        self._last_fps = 0
        self._fps_timer = QTimer(self)
        self._fps_timer.timeout.connect(self._update_fps)
        self._fps_timer.start(1000)
        self.metrics = MotionMetrics()
        self.last_sample_time = None

    def update_mac_list(self, mac_list, selected_macs=None):
        if selected_macs is None:
            selected_macs = set()

        updated = False
        for mac in mac_list:
            if mac not in self.mac_history:
                self.mac_history.append(mac)
                updated = True

        # 只有內容有變化時或選擇狀態改變時才刷新 QComboBox
        if self.mac_history != self._last_mac_list or hasattr(self, '_last_selected_macs') and selected_macs != self._last_selected_macs:
            current = self.mac_combo.currentText()
            self.mac_combo.blockSignals(True)
            self.mac_combo.clear()
            self.mac_combo.addItem("")  # 第一個選項為空白

            # 添加MAC地址，並為已選擇的設置不同樣式
            for mac in self.mac_history:
                self.mac_combo.addItem(mac)
                item_index = self.mac_combo.count() - 1

                # 如果這個MAC已被其他卡片選擇（但不是當前卡片）
                if mac in selected_macs and mac != self.connected_mac:
                    # 設置項目為禁用狀態和不同顏色
                    item = self.mac_combo.model().item(item_index)
                    if item:
                        item.setEnabled(False)
                        # 設置灰色背景和文字
                        item.setData(QColor(150, 150, 150), Qt.ItemDataRole.ForegroundRole)
                        item.setData(QColor(240, 240, 240), Qt.ItemDataRole.BackgroundRole)
                        item.setToolTip(f"MAC地址 {mac} 已被其他感測器使用")

            if current in self.mac_history or current == "":
                self.mac_combo.setCurrentText(current)
            elif self.mac_history and not self.connected_mac:
                # 只選擇未被使用的MAC
                available_macs = [mac for mac in self.mac_history if mac not in selected_macs]
                if available_macs:
                    self.mac_combo.setCurrentText(available_macs[0])
                    self.connected_mac = available_macs[0]
                    self.status_label.setText("已啟用")
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")
                    self.is_active = True
                    self.plotter.clear()
                    self.last_timestamp = None
                    self.record_sample_counter = 0
                    self.cur_mode = '6d'
                    self.update_plot_unit()
            elif not self.mac_history or current == "":
                self.mac_combo.setCurrentText("")
                self.connected_mac = None
                self.status_label.setText("未啟用")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.is_active = False
                self.plotter.clear()
            self.mac_combo.blockSignals(False)
            self._last_mac_list = list(self.mac_history)
            self._last_selected_macs = selected_macs.copy()

    def _on_mac_change(self):
        self.connected_mac = self.mac_combo.currentText()
        if not self.connected_mac:
            self.status_label.setText("未啟用")
            self.status_label.setObjectName("status_disconnected")
            self.status_indicator.set_status('disconnected')
            self.is_active = False
            self.plotter.clear()
            # 清空電量與四元數顯示
            self.batt_label.setText("電量: --%")
            self.quat_label.setText("四元數: --, --, --, --")
        else:
            self.status_label.setText("已啟用")
            self.status_label.setObjectName("status_connected")
            self.status_indicator.set_status('connected')
            self.is_active = True
            self.plotter.clear()
            self.last_timestamp = None
            self.record_sample_counter = 0
            self.cur_mode = '6d'
            self.update_plot_unit()

        # 通知MainWindow更新所有卡片的MAC列表
        if hasattr(self, 'parent') and hasattr(self.parent(), 'update_mac_lists'):
            self.parent().update_mac_lists()

        # 重新應用樣式
        self.status_label.style().unpolish(self.status_label)
        self.status_label.style().polish(self.status_label)

    def _on_mode_change(self):
        if self.connected_mac:
            if self._block_mode_signal:
                return
            if self.mode_fusion.isChecked():
                if self.cur_mode != 'fusion':
                    self.plotter.clear()
                self.cur_mode = 'fusion'
                self.update_plot_unit()
            else:
                if self.cur_mode != '6d':
                    self.plotter.clear()
                self.cur_mode = '6d'
                self.update_plot_unit()

    def update_plot_unit(self):
        self.plotter.cur_mode = self.cur_mode
        self.plotter.update_plot_unit()

    def add_sensor_data(self, parsed, mode, interval):
        self._fps_counter += 1  # 恢復FPS計數
        self.plotter.add_data(parsed)
        # 移除這裡的 update_plot() 調用，由定時器統一處理
        now = int(time.time() * 1000)  # ms, 系統時間
        self.record_buffer.append((parsed.copy(), now))

        # 當緩衝區滿了，且正在錄製時，一次性寫入
        if len(self.record_buffer) >= self.record_N:
            if self.file_path:
                try:
                    with open(self.file_path, 'a', newline='') as file:
                        writer = csv.writer(file)
                        for data, ts in self.record_buffer:
                            writer.writerow(self._get_scaled_row(data, ts))
                except Exception as e:
                    QMessageBox.critical(self, "寫入錯誤", str(e))
            self.record_buffer.clear() # 無論是否寫入，都清空緩衝區

        # 運動指標計算
        try:
            # 1. 取得原始加速度（單位g）和四元數
            acc_g = np.array([parsed.get('ax',0), parsed.get('ay',0), parsed.get('az',0)]) * 16.0 / 32768.0
            quat_wxyz = np.array([
                parsed.get('q0', 1.0), # w
                parsed.get('q1', 0.0), # x
                parsed.get('q2', 0.0), # y
                parsed.get('q3', 0.0)  # z
            ])

            # 2. 檢查數據有效性
            if not np.any(np.isnan(acc_g)) and not np.any(np.isnan(quat_wxyz)):
                if self.last_sample_time is not None:
                    dt = (now - self.last_sample_time) / 1000.0
                    if dt > 0:
                        # 3. 將原始數據傳給metrics模組，由其內部處理重力
                        self.metrics.update(acc_g, quat_wxyz, dt)

            self.last_sample_time = now
            # 4. 更新UI顯示
            max_speed = self.metrics.get_max_instant_speed()
            max_angle = self.metrics.get_max_angle()
            max_acc = self.metrics.get_max_explosive_acc()
            max_force_g = max_acc / 9.81

            # 更新標籤文字
            self.max_speed_label.setText(f"最大速度: {max_speed:.2f} m/s")
            # self.max_angle_label.setText(f"最大角度: {max_angle:.1f}°")  # 暫時隱藏角度
            self.max_instant_acc_label.setText(f"最大加速度: {max_acc:.2f} m/s²")
            self.max_force_label.setText(f"最大力度: {max_force_g:.2f} g")

            # 更新進度條 (設定合理的最大值範圍)
            speed_percent = min(int((max_speed / 50.0) * 100), 100)  # 假設最大速度50 m/s
            # angle_percent = min(int((max_angle / 360.0) * 100), 100)  # 最大角度360°  # 暫時隱藏角度
            acc_percent = min(int((max_acc / 300.0) * 100), 100)  # 假設最大加速度300 m/s²
            force_percent = min(int((max_force_g / 30.0) * 100), 100)  # 假設最大力度30g

            self.speed_progress.setValue(speed_percent)
            # self.angle_progress.setValue(angle_percent)  # 暫時隱藏角度
            self.acc_progress.setValue(acc_percent)
            self.force_progress.setValue(force_percent)

        except Exception as e:
            # 增加錯誤打印，方便調試
            print(f"[ERROR] Motion metrics calculation failed: {e}")
            pass

    def start_record(self):
        if not self.connected_mac:
            return
        formating_num = self.file_num.text()
        mac_suffix = self.connected_mac.replace(":","")[-4:] if self.connected_mac else "NONE"
        file_name = f"{self.file_name.text()}{mac_suffix}_{formating_num}.csv"
        data_dir = "data_log"
        os.makedirs(data_dir, exist_ok=True)
        self.file_path = os.path.join(data_dir, file_name)
        # header: timestamp, ax, ay, az, gx, gy, gz, roll, pitch, yaw, q0, q1, q2, q3
        header = [
            "timestamp", "ax", "ay", "az", "gx", "gy", "gz", "roll", "pitch", "yaw", "q0", "q1", "q2", "q3"
        ]
        with open(self.file_path, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(header)
        self.record_sample_counter = 0
        self.sample_counter = 0
        self.last_timestamp = None
        self.timestamp_counter = 0
        self._last_record_data = None
        self.record_buffer = []
        self.last_record_time = None
        self.last_record_ts = 0

    def stop_record(self):
        current_file_path = self.file_path
        self.file_path = None
        self.record_sample_counter = 0
        self.sample_counter = 0
        self.timestamp_counter = 0

        # 停止時將buffer剩餘資料寫入
        if self.record_buffer and current_file_path:
            try:
                with open(current_file_path, 'a', newline='') as file:
                    writer = csv.writer(file)
                    for data, ts in self.record_buffer:
                        writer.writerow(self._get_scaled_row(data, ts))
            except Exception as e:
                QMessageBox.critical(self, "寫入錯誤", str(e))
        self.record_buffer.clear()

        next_num = int(self.file_num.text() or '0') + 1
        self.file_num.setText(str(next_num))

    def _get_scaled_row(self, data, ts):
        row = [ts]
        header_keys = ["ax", "ay", "az", "gx", "gy", "gz", "roll", "pitch", "yaw", "q0", "q1", "q2", "q3"]
        for k in header_keys:
            raw_val = data.get(k, 0)

            scaled_val = raw_val
            if k in ['ax', 'ay', 'az']:
                scaled_val = raw_val * 16.0 / 32768.0
            elif k in ['gx', 'gy', 'gz']:
                scaled_val = raw_val * 2000.0 / 32768.0
            elif k in ['roll', 'pitch', 'yaw']:
                scaled_val = raw_val * 180.0 / 32768.0

            if isinstance(scaled_val, float):
                row.append(round(scaled_val, 5))
            else:
                row.append(scaled_val)
        return row

    def set_plotter_bg(self, theme):
        if theme == "light":
            self.plotter.set_background('w')
        else:
            self.plotter.set_background('#222')

    def _on_set_freq(self):
        freq_str = self.freq_input.text().strip()
        if not freq_str.isdigit():
            QMessageBox.warning(self, '格式錯誤', '請輸入正確的頻率數字')
            return
        if not self.connected_mac:
            QMessageBox.warning(self, '未連線', '請先連線感測器')
            return
        self.freq_set_requested.emit(self.connected_mac, freq_str)

    def set_battery(self, batt):
        if batt is not None:
            self.batt_label.setText(f"電量: {batt}%")
            if batt < 20:
                self.batt_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.batt_label.setStyleSheet("color: #1565c0; font-weight: bold;")

    def set_quaternion(self, parsed):
        if all(k in parsed for k in ['q0','q1','q2','q3']):
            q0 = round(parsed['q0'], 5)
            q1 = round(parsed['q1'], 5)
            q2 = round(parsed['q2'], 5)
            q3 = round(parsed['q3'], 5)
            self.quat_label.setText(f"四元數: {q0:.5f}, {q1:.5f}, {q2:.5f}, {q3:.5f}")
        else:
            self.quat_label.setText("四元數: --, --, --, --")

    def _update_fps(self):
        self._last_fps = self._fps_counter
        self.fps_label.setText(f"FPS: {self._last_fps}")
        self._fps_counter = 0

class MainWindow(QMainWindow):
    data_received = Signal(str, bytes)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("6DIMU Sensor Tools - Modern UI")
        self.setGeometry(50, 60, 1100, 750)  # 增加高度確保四個卡片完整顯示
        self._setup_ui()

    def _setup_ui(self):
        """設置現代化UI佈局"""
        central = QWidget()
        main_layout = QVBoxLayout(central)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 創建頁籤控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #3a3a4e;
                background: #1a1a2e;
            }
            QTabBar::tab {
                background: #2a2a3e;
                color: #ccc;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: #00d4ff;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: #3a3a4e;
            }
        """)

        # 創建實時監控頁籤（原有功能）
        self.realtime_tab = self._create_realtime_tab()
        self.tab_widget.addTab(self.realtime_tab, "🔴 實時監控")

        # 暫時隱藏未完成的分析功能頁籤
        # 創建數據分析頁籤
        # self.data_analysis_tab = DataAnalysisTab(self)
        # self.tab_widget.addTab(self.data_analysis_tab, "📊 數據分析")

        # 創建比較分析頁籤
        # self.comparison_tab = ComparisonTab(self)
        # self.tab_widget.addTab(self.comparison_tab, "⚖️ 比較分析")

        # 創建趨勢分析頁籤
        # self.trend_analysis_tab = TrendAnalysisTab(self)
        # self.tab_widget.addTab(self.trend_analysis_tab, "📈 趨勢分析")

        main_layout.addWidget(self.tab_widget)
        self.setCentralWidget(central)

        # 初始化其他組件
        self._init_components()

    def _create_realtime_tab(self):
        """創建實時監控頁籤（原有功能）"""
        tab = QWidget()
        main_layout = QHBoxLayout(tab)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 創建側邊欄和主內容區域，改為回傳 widget
        sidebar = self._create_sidebar_widget()
        content_widget = self._create_main_content_widget()

        main_layout.addWidget(sidebar, 0)         # 側邊欄不拉伸
        main_layout.addWidget(content_widget, 1)  # 主內容自動拉伸

        return tab

    def _create_sidebar_widget(self):
        """創建側邊欄並回傳 widget"""
        sidebar = QWidget()
        sidebar.setObjectName("sidebar")
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # 連接控制面板
        self.connection_panel = ConnectionPanel()
        sidebar_layout.addWidget(self.connection_panel)

        # 記錄控制區域
        record_section = SidebarSection("記錄控制")
        record_layout = QVBoxLayout()
        self.record_status_label = QLabel("未在紀錄")
        self.record_status_label.setObjectName("status_disconnected")
        record_layout.addWidget(self.record_status_label)
        self.record_btn = ModernButton('開始記錄', 'success')
        self.stop_btn = ModernButton('停止記錄', 'danger')
        record_layout.addWidget(self.record_btn)
        record_layout.addWidget(self.stop_btn)
        record_section.setLayout(record_layout)
        sidebar_layout.addWidget(record_section)

        # 數據管理區域
        data_section = SidebarSection("數據管理")
        data_layout = QVBoxLayout()

        # 重設按鈕區域
        reset_layout = QHBoxLayout()
        self.reset_btn = ModernButton("重設運動指標", "warning")
        self.reset_btn.setFixedWidth(120)  # 增加寬度確保文字完整顯示
        self.auto_reset_btn = ModernButton("自動重設", "primary")
        self.auto_reset_btn.setCheckable(True)  # 設為可切換按鈕
        self.auto_reset_btn.setFixedWidth(100)  # 增加寬度確保文字完整顯示
        reset_layout.addWidget(self.reset_btn)
        reset_layout.addWidget(self.auto_reset_btn)
        data_layout.addLayout(reset_layout)

        # 自動重設狀態顯示
        self.auto_reset_status = QLabel("自動重設: 關閉")
        self.auto_reset_status.setObjectName("status_disconnected")
        self.auto_reset_status.setStyleSheet("font-size: 11px; margin-top: 2px;")
        data_layout.addWidget(self.auto_reset_status)

        data_section.setLayout(data_layout)
        sidebar_layout.addWidget(data_section)

        # 數據轉發區域
        forward_section = SidebarSection("數據轉發")
        forward_layout = QVBoxLayout()

        # 協議選擇
        protocol_layout = QHBoxLayout()
        protocol_layout.addWidget(QLabel("協議:"))
        self.protocol_combo = ModernComboBox()
        self.protocol_combo.addItems(["UDP", "TCP", "WebSocket", "MQTT"])
        protocol_layout.addWidget(self.protocol_combo)
        forward_layout.addLayout(protocol_layout)

        # 目標地址
        host_layout = QHBoxLayout()
        host_layout.addWidget(QLabel("地址:"))
        self.host_input = QLineEdit("127.0.0.1")
        self.host_input.setFixedWidth(100)
        host_layout.addWidget(self.host_input)
        forward_layout.addLayout(host_layout)

        # 端口號
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))
        self.port_input = QLineEdit("8888")
        self.port_input.setFixedWidth(60)
        port_layout.addWidget(self.port_input)
        forward_layout.addLayout(port_layout)

        # 轉發控制按鈕
        self.forward_btn = ModernButton("啟動轉發", "success")
        self.stop_forward_btn = ModernButton("停止轉發", "danger")
        forward_layout.addWidget(self.forward_btn)
        forward_layout.addWidget(self.stop_forward_btn)

        # 轉發狀態
        self.forward_status_label = QLabel("轉發已停用")
        self.forward_status_label.setObjectName("status_disconnected")
        forward_layout.addWidget(self.forward_status_label)

        forward_section.setLayout(forward_layout)
        sidebar_layout.addWidget(forward_section)

        # 主題切換 - 暫時隱藏
        # theme_section = SidebarSection("外觀設置")
        # theme_layout = QVBoxLayout()
        # self.theme_btn = ModernButton("切換主題", "primary")
        # theme_layout.addWidget(self.theme_btn)
        # theme_section.setLayout(theme_layout)
        # sidebar_layout.addWidget(theme_section)

        sidebar_layout.addStretch()
        sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        return sidebar

    def _create_main_content_widget(self):
        """創建主內容區域並回傳 widget"""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 2, 20, 2)  # 進一步減少頂部和底部邊距
        content_layout.setSpacing(4)  # 進一步減少間距
        title_label = QLabel("IMU感測器監控面板")
        title_label.setObjectName("main_title")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(0, 212, 255, 0.1), stop:1 rgba(0, 212, 255, 0.05));
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            padding: 6px 10px;
            margin-bottom: 4px;
        """)
        content_layout.addWidget(title_label)
        cards_widget = QWidget()
        grid = QGridLayout(cards_widget)
        grid.setSpacing(8)  # 進一步減少卡片間距
        grid.setContentsMargins(0, 0, 0, 0)  # 移除網格邊距
        self.cards = [SensorCard(i, parent=self) for i in range(SENSOR_MAX)]
        for i, card in enumerate(self.cards):
            row, col = divmod(i, 2)
            grid.addWidget(card, row, col)
            card.freq_set_requested.connect(self._on_freq_set_requested)
        content_layout.addWidget(cards_widget)
        content_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        return content_widget

    def _init_components(self):
        """初始化組件和連接信號"""
        # 連接信號
        self.connection_panel.connect_requested.connect(self.connect_serial)
        self.connection_panel.disconnect_requested.connect(self.disconnect_serial)
        self.connection_panel.refresh_requested.connect(self.refresh_ports)

        self.record_btn.clicked.connect(self.record_all)
        self.stop_btn.clicked.connect(self.stop_all)
        self.reset_btn.clicked.connect(self.reset_all_metrics)
        # self.theme_btn.clicked.connect(self.toggle_theme)  # 暫時隱藏主題切換

        # 自動重設信號連接
        self.auto_reset_btn.clicked.connect(self.toggle_auto_reset)

        # 數據轉發信號連接
        self.forward_btn.clicked.connect(self.start_data_forwarding)
        self.stop_forward_btn.clicked.connect(self.stop_data_forwarding)

        # 為了兼容性，創建舊的引用
        self.port_combo = self.connection_panel.port_combo
        self.refresh_btn = self.connection_panel.refresh_btn
        self.connect_btn = self.connection_panel.connect_btn
        self.disconnect_btn = self.connection_panel.disconnect_btn

        # 主題樣式與模式必須在 set_theme 前定義
        self.light_theme = """
QWidget { background: #f5f5f5; color: #222; }
QPushButton { background: #e0e0e0; color: #222; border: 1px solid #bbb; }
QPushButton:disabled { color: #aaa; background: #ccc; }
QLineEdit, QComboBox {
    background: #fff;
    color: #222;
    border: 1px solid #bbb;
    selection-background-color: #cce6ff;
}
QRadioButton {
    color: #1565c0;
    font-weight: bold;
    background: transparent;
    border-radius: 4px;
    padding: 2px 6px;
}
QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border-radius: 8px;
    border: 2px solid #1565c0;
    background: #fff;
}
QRadioButton::indicator:checked {
    background: #1565c0;
    border: 2px solid #1565c0;
}
QRadioButton:checked {
    background: #e3f2fd;
}
QGroupBox { border: 2px solid #0078d7; }
QLabel { color: #222; }
"""
        self.dark_theme = """
QWidget { background: #222; color: #eee; }
QPushButton { background: #444; color: #fff; }
QPushButton:disabled { color: #888; background: #222; }
QLineEdit, QComboBox {
    background: #333;
    color: #eee;
    border: 1px solid #555;
    selection-background-color: #225577;
}
QRadioButton {
    color: #90caf9;
    font-weight: bold;
    background: transparent;
    border-radius: 4px;
    padding: 2px 6px;
}
QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border-radius: 8px;
    border: 2px solid #90caf9;
    background: #222;
}
QRadioButton::indicator:checked {
    background: #90caf9;
    border: 2px solid #90caf9;
}
QRadioButton:checked {
    background: #222e3a;
}
QGroupBox { border: 2px solid #0078d7; }
QLabel { color: #eee; }
"""
        # 使用現代化樣式
        self.theme_mode = "light"  # 預設亮色主題
        self.set_theme(self.theme_mode)

        # 初始化其他組件
        self.data_received.connect(self.on_serial_data)
        self.serial_mgr = SerialManager(on_data_callback=self._thread_safe_data_callback, on_disconnect_callback=self.on_serial_disconnect)

        # 初始化數據轉發器
        self.data_forwarder = DataForwarder()
        self.data_forwarder.status_changed.connect(self._on_forward_status_changed)
        self.data_forwarder.error_occurred.connect(self._on_forward_error)
        self.stop_forward_btn.setEnabled(False)
        self._current_battery = {}  # 存儲每個MAC的電池電量

        # 初始化自動重設功能
        self.auto_reset_enabled = False
        self.last_motion_time = {}  # 記錄每個MAC最後有運動的時間
        self.auto_reset_timer = QTimer()
        self.auto_reset_timer.timeout.connect(self._check_auto_reset)
        self.auto_reset_timer.start(1000)  # 每秒檢查一次
        self.motion_threshold = 2.0  # 運動閾值 (g) - 降低閾值使其更敏感

        self.refresh_ports()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_mac_lists)
        self.timer.start(500)
        self.is_serial_connected = False
        self.stop_btn.setEnabled(False)
        self._debug_fps_counter = {}  # mac: count
        self._debug_fps_timer = QTimer()
        self._debug_fps_timer.timeout.connect(self._print_debug_fps)
        self._debug_fps_timer.start(1000)
        self._last_plot_time = time.time()
        self._plot_fps_counter = 0
        self._plot_fps = 0
        self.plot_timer = QTimer()
        self.plot_timer.timeout.connect(self._refresh_all_plots)
        self.plot_timer.start(16)  # 約 60 FPS

    def _thread_safe_data_callback(self, mac, data_bytes):
        self.data_received.emit(mac, data_bytes)

    def refresh_ports(self):
        self.refresh_btn.setText("刷新中...")
        self.refresh_btn.setEnabled(False)
        QApplication.processEvents()  # 立即刷新UI
        ports = SerialManager.list_ports()
        self.connection_panel.set_ports(ports)
        QTimer.singleShot(500, self._reset_refresh_btn)

    def _reset_refresh_btn(self):
        self.refresh_btn.setText("刷新")
        self.refresh_btn.setEnabled(True)

    def connect_serial(self, port=None):
        if port is None:
            port = self.port_combo.currentText()
        if not port:
            QMessageBox.warning(self, "錯誤", "請選擇 Serial Port")
            return

        # 設置連接中狀態
        self.connection_panel.status_indicator.set_status('connecting')

        ok = self.serial_mgr.connect(port)
        if ok:
            self.is_serial_connected = True
            self.statusBar().showMessage(f"已連線: {port}")
            self.connection_panel.set_connection_status(True)
        else:
            QMessageBox.critical(self, "連線失敗", f"無法連線到 {port}")
            self.connection_panel.set_connection_status(False)

    def disconnect_serial(self):
        self.serial_mgr.disconnect()
        self.is_serial_connected = False
        self.statusBar().showMessage("已斷線")
        self.connection_panel.set_connection_status(False)

    def on_serial_disconnect(self, msg):
        self.is_serial_connected = False
        self.statusBar().showMessage(f"Serial 斷線: {msg}")
        self.connection_panel.set_connection_status(False)

    def update_mac_lists(self):
        macs = self.serial_mgr.get_mac_list()
        # 收集所有已選擇的MAC地址
        selected_macs = set()
        for card in self.cards:
            if card.connected_mac:
                selected_macs.add(card.connected_mac)

        # 更新每個卡片的MAC列表，傳遞已選擇的MAC信息
        for card in self.cards:
            card.update_mac_list(macs, selected_macs)

    def on_serial_data(self, mac, data_bytes):
        i = 0
        parsed_list = []
        while i < len(data_bytes):
            # 資料同步：尋找正確開頭
            if len(data_bytes) - i >= 3 and data_bytes[i] == 0xAA and data_bytes[i+1] == 0xBB:
                sys_type = data_bytes[i+2]
                batt = data_bytes[i+3]  # 電池電量
                if len(data_bytes) - i >= 30:  # 新格式固定長度為30bytes
                    try:
                        six_d = {
                            'ax': int.from_bytes(data_bytes[i+4:i+6], 'big', signed=True),
                            'ay': int.from_bytes(data_bytes[i+6:i+8], 'big', signed=True),
                            'az': int.from_bytes(data_bytes[i+8:i+10], 'big', signed=True),
                            'gx': int.from_bytes(data_bytes[i+10:i+12], 'big', signed=True),
                            'gy': int.from_bytes(data_bytes[i+12:i+14], 'big', signed=True),
                            'gz': int.from_bytes(data_bytes[i+14:i+16], 'big', signed=True),
                        }
                        fusion = {
                            'roll': int.from_bytes(data_bytes[i+16:i+18], 'big', signed=True),
                            'pitch': int.from_bytes(data_bytes[i+18:i+20], 'big', signed=True),
                            'yaw': int.from_bytes(data_bytes[i+20:i+22], 'big', signed=True),
                        }
                        quat = {
                            'q0': int.from_bytes(data_bytes[i+22:i+24], 'big', signed=True) / 32768.0,
                            'q1': int.from_bytes(data_bytes[i+24:i+26], 'big', signed=True) / 32768.0,
                            'q2': int.from_bytes(data_bytes[i+26:i+28], 'big', signed=True) / 32768.0,
                            'q3': int.from_bytes(data_bytes[i+28:i+30], 'big', signed=True) / 32768.0,
                        }
                        all_data = {**six_d, **fusion, **quat}
                        for card in self.cards:
                            if card.connected_mac == mac:
                                parsed_list.append((all_data, 'all', batt, card))
                    except Exception as e:
                        print(f"[警告] 資料解析失敗: {e}")
                    i += 30
                else:
                    print(f"[警告] 資料長度不足: {len(data_bytes) - i}")
                    break
            else:
                # 若不是正確開頭，跳到下一個byte
                i += 1
        n = len(parsed_list)
        if n == 0:
            return
        interval = 10  # ms
        for (parsed, mode, batt, card) in parsed_list:
            # 保存電池電量信息供轉發使用
            self._current_battery[mac] = batt
            card.set_battery(batt)
            card.set_quaternion(parsed)
            self._dispatch_sensor_data(mac, parsed, mode, interval)

    def _print_debug_fps(self):
        for mac, count in self._debug_fps_counter.items():
            print(f"[DEBUG] MAC: {mac} Data FPS: {count}")
        self._debug_fps_counter.clear()

    def _dispatch_sensor_data(self, mac, parsed, mode, interval=10):
        self._debug_fps_counter[mac] = self._debug_fps_counter.get(mac, 0) + 1

        # 檢測運動狀態（用於自動重設）
        if self.auto_reset_enabled:
            self._check_motion_for_auto_reset(mac, parsed)

        # 轉發數據到外部系統
        if self.data_forwarder.enabled:
            timestamp = int(time.time() * 1000)  # 毫秒時間戳
            # 從解析的數據中獲取電池電量，如果沒有則設為0
            battery = getattr(self, '_current_battery', {}).get(mac, 0)
            self.data_forwarder.forward_data(mac, parsed, battery, timestamp)

        for card in self.cards:
            if card.connected_mac == mac:
                card.add_sensor_data(parsed, mode, interval)

    def send_mode_switch(self, mac, cmd):
        if self.is_serial_connected:
            self.serial_mgr.send_nus_tx(mac, cmd)

    def record_all(self):
        for card in self.cards:
            if card.connected_mac:
                card.start_record()
        self.record_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        # 顯示紅色紀錄中提示
        self.record_status_label.setText("● 正在紀錄數據")
        self.record_status_label.setObjectName("status_connected")
        self.record_status_label.style().unpolish(self.record_status_label)
        self.record_status_label.style().polish(self.record_status_label)

    def stop_all(self):
        for card in self.cards:
            if card.connected_mac:
                card.stop_record()
        self.record_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        # 顯示灰色未在紀錄
        self.record_status_label.setText("未在紀錄")
        self.record_status_label.setObjectName("status_disconnected")
        self.record_status_label.style().unpolish(self.record_status_label)
        self.record_status_label.style().polish(self.record_status_label)

    def set_theme(self, theme):
        """設置主題樣式"""
        if theme == "dark":
            # 使用現代化深色主題
            self.setStyleSheet(ModernStyles.get_complete_style())
        else:
            # 保留原有的亮色主題作為備選
            self.setStyleSheet(self.light_theme)
        # 同步所有卡片的 plotter 背景色
        for card in self.cards:
            card.set_plotter_bg(theme)

    def toggle_theme(self):
        self.theme_mode = "light" if self.theme_mode == "dark" else "dark"
        self.set_theme(self.theme_mode)

    def _refresh_all_plots(self):
        for card in self.cards:
            card.plotter.update_plot()
        self._plot_fps_counter += 1
        now = time.time()
        if now - self._last_plot_time >= 1.0:
            self._plot_fps = self._plot_fps_counter
            print(f"[DEBUG] Plot FPS: {self._plot_fps}")
            self._plot_fps_counter = 0
            self._last_plot_time = now

    def _on_freq_set_requested(self, mac, freq):
        if hasattr(self, 'serial_mgr') and self.serial_mgr:
            self.serial_mgr.send_nus_tx(mac, f"freq={freq}")
        else:
            QMessageBox.warning(self, '錯誤', '找不到 serial 發送方法')

    def reset_all_metrics(self):
        # 暫時禁用按鈕並更改文字
        self.reset_btn.setText("重設中...")
        self.reset_btn.setEnabled(False)

        for card in self.cards:
            card.metrics.reset()
            card.max_speed_label.setText("最大速度: -- m/s")
            # card.max_angle_label.setText("最大角度: --°")  # 暫時隱藏角度
            card.max_instant_acc_label.setText("最大加速度: -- m/s²")
            card.max_force_label.setText("最大力度: -- g")
            # 重設進度條
            card.speed_progress.setValue(0)
            # card.angle_progress.setValue(0)  # 暫時隱藏角度
            card.acc_progress.setValue(0)
            card.force_progress.setValue(0)
            card.last_sample_time = None

        # 300ms後恢復按鈕狀態
        QTimer.singleShot(150, self._restore_reset_button)

    def _restore_reset_button(self):
        self.reset_btn.setText("重設運動指標")
        self.reset_btn.setEnabled(True)

    def start_data_forwarding(self):
        """啟動數據轉發"""
        protocol = self.protocol_combo.currentText()
        host = self.host_input.text().strip()
        port_text = self.port_input.text().strip()

        if not host:
            QMessageBox.warning(self, "錯誤", "請輸入目標地址")
            return

        try:
            port = int(port_text)
        except ValueError:
            QMessageBox.warning(self, "錯誤", "請輸入有效的端口號")
            return

        # 配置轉發器
        self.data_forwarder.configure(protocol, host, port)

        # 啟動轉發
        if self.data_forwarder.start_forwarding():
            self.forward_btn.setEnabled(False)
            self.stop_forward_btn.setEnabled(True)
            self.forward_status_label.setText(f"轉發中 ({protocol})")
            self.forward_status_label.setObjectName("status_connected")
            self.forward_status_label.style().unpolish(self.forward_status_label)
            self.forward_status_label.style().polish(self.forward_status_label)

    def stop_data_forwarding(self):
        """停止數據轉發"""
        self.data_forwarder.stop_forwarding()
        self.forward_btn.setEnabled(True)
        self.stop_forward_btn.setEnabled(False)
        self.forward_status_label.setText("轉發已停用")
        self.forward_status_label.setObjectName("status_disconnected")
        self.forward_status_label.style().unpolish(self.forward_status_label)
        self.forward_status_label.style().polish(self.forward_status_label)

    def _on_forward_status_changed(self, protocol, status):
        """轉發狀態變化回調"""
        self.forward_status_label.setText(f"{protocol}: {status}")

    def _on_forward_error(self, error_msg):
        """轉發錯誤回調"""
        QMessageBox.critical(self, "轉發錯誤", error_msg)
        self.stop_data_forwarding()

    def toggle_auto_reset(self):
        """切換自動重設功能"""
        self.auto_reset_enabled = self.auto_reset_btn.isChecked()

        if self.auto_reset_enabled:
            self.auto_reset_status.setText("自動重設: 開啟 (初始化中...)")
            self.auto_reset_status.setObjectName("status_connected")
            self.auto_reset_btn.setText("自動重設 ✓")
            # 初始化所有MAC的運動時間
            current_time = time.time()
            for card in self.cards:
                if card.connected_mac:
                    self.last_motion_time[card.connected_mac] = current_time
            print(f"[INFO] 自動重設已啟用，監控 {len(self.last_motion_time)} 個感測器")
        else:
            self.auto_reset_status.setText("自動重設: 關閉")
            self.auto_reset_status.setObjectName("status_disconnected")
            self.auto_reset_btn.setText("自動重設")
            self.last_motion_time.clear()
            print("[INFO] 自動重設已關閉")

        # 重新應用樣式
        self.auto_reset_status.style().unpolish(self.auto_reset_status)
        self.auto_reset_status.style().polish(self.auto_reset_status)

    def _check_motion_for_auto_reset(self, mac, parsed):
        """檢查運動狀態，用於自動重設"""
        try:
            # 計算加速度向量的大小
            ax = parsed.get('ax', 0) * 16.0 / 32768.0  # 轉換為g
            ay = parsed.get('ay', 0) * 16.0 / 32768.0
            az = parsed.get('az', 0) * 16.0 / 32768.0

            # 計算總加速度大小
            total_acc_magnitude = (ax**2 + ay**2 + az**2)**0.5

            # 計算與重力的偏差（重力約為1g）
            gravity_deviation = abs(total_acc_magnitude - 1.0)

            # 計算陀螺儀數據（角速度）
            gx = abs(parsed.get('gx', 0) * 2000.0 / 32768.0)  # 轉換為°/s
            gy = abs(parsed.get('gy', 0) * 2000.0 / 32768.0)
            gz = abs(parsed.get('gz', 0) * 2000.0 / 32768.0)
            max_gyro = max(gx, gy, gz)

            # 如果加速度偏差大於閾值或陀螺儀檢測到旋轉，視為有運動
            is_moving = (gravity_deviation > self.motion_threshold) or (max_gyro > 90.0)  # 90°/s 旋轉閾值

            # 確保MAC在字典中
            if mac not in self.last_motion_time:
                self.last_motion_time[mac] = time.time()

            if is_moving:
                self.last_motion_time[mac] = time.time()
                # 調試信息（可選）
                # print(f"[DEBUG] {mac} 運動檢測: 重力偏差={gravity_deviation:.3f}g, 最大角速度={max_gyro:.1f}°/s")

        except Exception as e:
            print(f"[ERROR] 運動檢測失敗: {e}")

    def _check_auto_reset(self):
        """檢查是否需要自動重設"""
        if not self.auto_reset_enabled:
            return

        current_time = time.time()
        active_macs = []

        # 收集所有活躍的MAC地址
        for card in self.cards:
            if card.connected_mac:
                active_macs.append(card.connected_mac)
                # 確保MAC在運動時間字典中
                if card.connected_mac not in self.last_motion_time:
                    self.last_motion_time[card.connected_mac] = current_time

        if not active_macs:
            return

        # 檢查所有活躍的感測器是否都靜止超過5秒
        all_static = True
        static_times = []

        for mac in active_macs:
            static_duration = current_time - self.last_motion_time[mac]
            static_times.append(f"{mac[-2:]}:{static_duration:.1f}s")

            if static_duration < 5.0:  # 5秒閾值
                all_static = False

        # 更新狀態顯示
        if active_macs:
            status_text = f"自動重設: 開啟 ({', '.join(static_times)})"
            self.auto_reset_status.setText(status_text)

        # 如果所有感測器都靜止超過5秒，執行自動重設
        if all_static and len(active_macs) > 0:
            print(f"[INFO] 自動重設觸發 - 所有感測器靜止超過5秒")
            self.reset_all_metrics()
            # 重新初始化運動時間，避免立即再次觸發
            for mac in active_macs:
                self.last_motion_time[mac] = current_time

if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec())
